// src/components/QPTBuyback/UnifiedRoomCard/components/ParticipationStatus.jsx
// 参与状态显示组件

import React, { useState } from 'react'
import { toast } from 'react-hot-toast'

const ParticipationStatus = ({
  room,
  account,
  userRole,
  isWinner,
  isRoomFull,
  isExpired,
  qptVerification,
  refundStatus,
  winnerRewardStatus,
  refundInfo,
  isRefunding,
  onClaimReward,
  onClaimWinnerReward,
  handleRefund,
  isClaimingReward,
  isAdminView
}) => {
  const [isProcessingExpiry, setIsProcessingExpiry] = useState(false)

  // 处理过期房间
  const handleProcessExpiry = async () => {
    if (isProcessingExpiry) return

    setIsProcessingExpiry(true)
    try {
      const { finalizeTimeoutRoom } = await import('@/services/qptBuybackService')
      await finalizeTimeoutRoom(room.id, account)

      toast.success('过期房间处理成功！参与者现在可以申请退款')

      // 刷新页面或触发重新获取数据
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    } catch (error) {
      console.error('处理过期房间失败:', error)

      let errorMessage = '处理过期房间失败'
      if (error.message.includes('User rejected')) {
        errorMessage = '用户取消了交易'
      } else if (error.message.includes('Not expired')) {
        errorMessage = '房间还未过期'
      } else if (error.message.includes('Not locked')) {
        errorMessage = '房间已经处理过期了'
      }

      toast.error(errorMessage)
    } finally {
      setIsProcessingExpiry(false)
    }
  }
  // QPT回购房间不显示获胜者状态提示，奖励信息在RewardInfo组件中显示
  if (isWinner) {
    return null
  }

  // 检查是否开奖已完成
  const isStep2Complete = room.winner && room.winner !== '0x0000000000000000000000000000000000000000'

  // 参与者状态
  if (userRole === 'participant') {
    // 如果开奖已完成，不显示参与者状态区域
    if (isStep2Complete) {
      return null
    }
    return (
      <div className="participation-status participant-status">
        {isRoomFull ? (
          // 房间满员时的状态
          isExpired ? (
            // 满员过期房间
            <div className="expired-status">
              <div className="status-header">
                <span className="status-icon">⚠️</span>
                <span className="status-text">房间已过期</span>
              </div>
              <div className="status-details">
                {refundStatus && refundStatus.canRefund ? (
                  <p>房间满员但已过期，可申请退款</p>
                ) : (
                  <p>房间满员但已过期，需要先处理过期状态</p>
                )}
                {qptVerification && (
                  <p className={`verification-status ${qptVerification.isValid ? 'valid' : 'invalid'}`}>
                    {qptVerification.message}
                  </p>
                )}
              </div>

              {/* 过期房间的退款按钮 */}
              {refundStatus && !refundStatus.isAlreadyRefunded && refundStatus.canRefund && (
                <button
                  onClick={handleRefund}
                  disabled={isRefunding}
                  className="action-button claim-button"
                  style={{
                    marginTop: '12px',
                    width: '100%',
                    padding: '12px 24px',
                    fontSize: '16px',
                    fontWeight: '600',
                    backgroundColor: isRefunding ? '#6c757d' : '#dc3545',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: isRefunding ? 'not-allowed' : 'pointer',
                    opacity: isRefunding ? 0.7 : 1,
                    transition: 'all 0.3s ease',
                    minHeight: '48px'
                  }}
                >
                  {isRefunding ? '处理中...' : `🔄 申请退款 (${refundStatus.principalAmount?.toFixed(2) || '0.00'} QPT本金)`}
                </button>
              )}

              {/* 处理过期按钮 - 当房间过期但还未处理时显示 */}
              {refundStatus && !refundStatus.canRefund && !refundStatus.isAlreadyRefunded &&
               refundStatus.reason && refundStatus.reason.includes('not processed yet') && (
                <button
                  onClick={handleProcessExpiry}
                  disabled={isProcessingExpiry}
                  className="action-button"
                  style={{
                    marginTop: '12px',
                    width: '100%',
                    padding: '12px 24px',
                    fontSize: '16px',
                    fontWeight: '600',
                    backgroundColor: isProcessingExpiry ? '#6c757d' : '#f59e0b',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: isProcessingExpiry ? 'not-allowed' : 'pointer',
                    opacity: isProcessingExpiry ? 0.7 : 1,
                    transition: 'all 0.3s ease',
                    minHeight: '48px'
                  }}
                >
                  {isProcessingExpiry ? '处理中...' : '⚙️ 处理过期状态'}
                </button>
              )}

              {/* 显示状态说明 */}
              {refundStatus && !refundStatus.canRefund && !refundStatus.isAlreadyRefunded && (
                <div style={{
                  marginTop: '12px',
                  padding: '12px',
                  background: 'rgba(255, 193, 7, 0.1)',
                  border: '1px solid rgba(255, 193, 7, 0.3)',
                  borderRadius: '8px',
                  fontSize: '14px',
                  color: '#856404'
                }}>
                  {refundStatus.reason || '暂不可申请退款'}
                </div>
              )}

              {refundStatus?.isAlreadyRefunded && (
                <div className="claimed-status">
                  <span className="claimed-icon">✅</span>
                  <span>已申请退款</span>
                </div>
              )}
            </div>
          ) : (
            // 满员未过期：等待发起人开奖
            <div className="waiting-lottery-status">
              <div className="status-header">
                <span className="status-icon">⏳</span>
                <span className="status-text">等待发起人开奖</span>
              </div>
              <div className="status-details">
                <p>房间已满员，发起人正在准备开奖</p>
                <p>开奖结果将由区块链交易哈希随机决定</p>
                {qptVerification && (
                  <p className={`verification-status ${qptVerification.isValid ? 'valid' : 'invalid'}`}>
                    {qptVerification.message}
                  </p>
                )}
              </div>

              {/* 满员房间的领取按钮 */}
              {refundInfo?.canRefund && refundInfo.refundType === 2 && (
                <button
                  onClick={() => onClaimReward?.(room)}
                  disabled={isClaimingReward}
                  className="action-button claim-button"
                  style={{
                    marginTop: '12px',
                    width: '100%',
                    padding: '12px 24px',
                    fontSize: '16px',
                    fontWeight: '600',
                    backgroundColor: isClaimingReward ? '#6c757d' : '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: isClaimingReward ? 'not-allowed' : 'pointer',
                    opacity: isClaimingReward ? 0.7 : 1,
                    transition: 'all 0.3s ease',
                    minHeight: '48px'
                  }}
                >
                  {isClaimingReward ? '领取中...' : `💰 领取本金+补贴 (${refundInfo.totalAmount?.toFixed(2)} QPT)`}
                </button>
              )}
            </div>
          )
        ) : (
          // 未满员：正常参与状态
          <div className="waiting-status">
            <div className="status-header">
              <span className="status-icon">✅</span>
              <span className="status-text">您已参与此房间</span>
            </div>
            <div className="status-details">
              <p>等待其他人加入，满员后即可开奖</p>
              <p>当前人数：{room.participantsCount || 0}/8</p>
              {qptVerification && (
                <p className={`verification-status ${qptVerification.isValid ? 'valid' : 'invalid'}`}>
                  {qptVerification.message}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 创建者状态 - 在管理员视图中不显示
  if (userRole === 'creator' && !isAdminView) {
    return (
      <div className="participation-status creator-status">
        <div className="status-header">
          <span className="status-icon">👑</span>
          <span className="status-text">您是房间创建者</span>
        </div>
        <div className="status-details">
          {isRoomFull && isExpired ? (
            <p>房间满员但已过期，需要处理过期状态</p>
          ) : (
            <>
              <p>等待参与者加入房间</p>
              <p>当前人数：{room.participantsCount || 0}/8</p>
            </>
          )}
        </div>

        {/* 创建者处理过期按钮 */}
        {isRoomFull && isExpired && room.locked && (
          <button
            onClick={handleProcessExpiry}
            disabled={isProcessingExpiry}
            className="action-button"
            style={{
              marginTop: '12px',
              width: '100%',
              padding: '12px 24px',
              fontSize: '16px',
              fontWeight: '600',
              backgroundColor: isProcessingExpiry ? '#6c757d' : '#f59e0b',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: isProcessingExpiry ? 'not-allowed' : 'pointer',
              opacity: isProcessingExpiry ? 0.7 : 1,
              transition: 'all 0.3s ease',
              minHeight: '48px'
            }}
          >
            {isProcessingExpiry ? '处理中...' : '⚙️ 处理过期状态'}
          </button>
        )}
      </div>
    )
  }

  return null
}

export default ParticipationStatus
