# QPT回购房间退款按钮修复测试

## 问题描述
QPT回购房间，房间状态已过期，房间未满员，过期后参与者可以申请退款，但是在参与者视图中，QPT回购房间中没有显示退款按钮。

## 问题根源
1. **满员过期房间**需要先调用`finalizeTimeout`函数处理过期状态
2. 只有当`room.locked = false`时，合约的`getRefundStatus`才会返回`canRefund=true, refundType=3`
3. 前端缺少处理过期状态的功能

## 修复方案
在`ParticipationStatus.jsx`组件中添加：

### 1. 处理过期状态的函数
```javascript
const handleProcessExpiry = async () => {
  // 调用 finalizeTimeoutRoom 处理过期状态
  // 成功后刷新页面让用户看到退款按钮
}
```

### 2. 满员过期房间的处理逻辑
- **如果可以退款**：显示退款按钮
- **如果不能退款且原因是"not processed yet"**：显示"处理过期状态"按钮
- **其他情况**：显示状态说明

### 3. 创建者的处理过期按钮
为房间创建者也添加处理过期状态的按钮

## 测试步骤

### 测试场景1：满员过期未处理的房间
1. 找到一个满员过期但`locked=true`的房间
2. 参与者应该看到"处理过期状态"按钮
3. 点击按钮后应该调用`finalizeTimeout`
4. 处理成功后页面刷新，显示退款按钮

### 测试场景2：满员过期已处理的房间
1. 找到一个满员过期且`locked=false`的房间
2. 参与者应该直接看到退款按钮
3. 点击退款按钮应该调用`refundExpired`函数

### 测试场景3：未满员过期的房间
1. 找到一个未满员过期的房间
2. 参与者应该直接看到退款按钮
3. 点击退款按钮应该调用`refundFailed`函数

## 验证要点
1. ✅ 修复了满员过期房间不显示退款按钮的问题
2. ✅ 添加了处理过期状态的功能
3. ✅ 为创建者和参与者都提供了相应的操作按钮
4. ✅ 保持了原有的退款逻辑不变

## 相关文件
- `qupintuan/src/components/QPTBuyback/UnifiedRoomCard/components/ParticipationStatus.jsx`
- `qupintuan/hardhat/contracts/QPTBuyback.sol` (合约逻辑)
- `qupintuan/src/services/qptBuybackService.js` (finalizeTimeoutRoom函数)
